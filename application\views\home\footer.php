    </div> <!-- End container -->

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-gamepad me-2"></i>GamesPlatform</h5>
                    <p class="mb-0">A melhor plataforma de games online do Brasil</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; <?= date('Y') ?> GamesPlatform. Todos os direitos reservados.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Add Balance Modal
        function showAddBalanceModal() {
            const modal = new bootstrap.Modal(document.getElementById('addBalanceModal'));
            modal.show();
        }

        // Add Balance Form Handler
        document.getElementById('addBalanceForm')?.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const amount = formData.get('amount');
            
            if (!amount || amount <= 0) {
                alert('Por favor, insira um valor válido');
                return;
            }
            
            try {
                const response = await fetch('<?= base_url("add_balance") ?>', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.status === 1) {
                    // Update balance display with animation
                    const balanceDisplay = document.querySelector('.balance-display');
                    if (balanceDisplay) {
                        balanceDisplay.innerHTML = '<i class="fas fa-coins me-1"></i>R$ ' +
                            new Intl.NumberFormat('pt-BR', { minimumFractionDigits: 2 }).format(data.new_balance);

                        // Add success animation
                        balanceDisplay.style.transform = 'scale(1.2)';
                        balanceDisplay.style.backgroundColor = '#10b981';
                        balanceDisplay.style.transition = 'all 0.3s ease';

                        setTimeout(() => {
                            balanceDisplay.style.transform = 'scale(1)';
                            balanceDisplay.style.backgroundColor = '';
                        }, 500);
                    }

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addBalanceModal'));
                    modal.hide();

                    // Show success message
                    showAlert('success', data.msg);

                    // Reset form
                    this.reset();

                    // Trigger a full balance sync to ensure consistency
                    setTimeout(() => syncBalance(false), 1000);
                } else {
                    showAlert('danger', data.msg);
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('danger', 'Erro ao adicionar saldo. Tente novamente.');
            }
        });

        // Show Alert Function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Game Launch Function
        function launchGame(gameCode, gameName) {
            // Check if user has balance
            const balanceText = document.querySelector('.balance-display')?.textContent;
            const balance = parseFloat(balanceText?.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
            
            if (balance <= 0) {
                showInsufficientBalanceModal();
                return;
            }
            
            // Launch game in new window
            const gameUrl = `<?= base_url("play/") ?>${gameCode}`;
            window.open(gameUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }

        // Insufficient Balance Modal
        function showInsufficientBalanceModal() {
            const modalHtml = `
                <div class="modal fade" id="insufficientBalanceModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Saldo Insuficiente
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <i class="fas fa-coins fa-3x text-warning mb-3"></i>
                                <h5>Você não possui saldo suficiente para jogar!</h5>
                                <p class="text-muted">Adicione saldo à sua conta ou compre créditos API para continuar jogando.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                                <button type="button" class="btn btn-primary" onclick="showAddBalanceModal(); bootstrap.Modal.getInstance(document.getElementById('insufficientBalanceModal')).hide();">
                                    <i class="fas fa-plus-circle me-1"></i>Adicionar Saldo
                                </button>
                                <a href="<?= base_url('api_purchase') ?>" class="btn btn-warning">
                                    <i class="fas fa-shopping-cart me-1"></i>Comprar API
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Remove existing modal if any
            const existingModal = document.getElementById('insufficientBalanceModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('insufficientBalanceModal'));
            modal.show();
            
            // Remove modal from DOM when hidden
            document.getElementById('insufficientBalanceModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Balance synchronization function
        async function syncBalance(showNotification = false) {
            const syncIcon = document.getElementById('syncIcon');
            const balanceDisplay = document.querySelector('.balance-display');

            try {
                // Show sync icon
                if (syncIcon) {
                    syncIcon.classList.remove('d-none');
                    syncIcon.style.animation = 'spin 1s linear infinite';
                }

                const response = await fetch('<?= base_url("get_balance") ?>');
                const data = await response.json();

                if (data.status === 1) {
                    if (balanceDisplay) {
                        const oldBalance = balanceDisplay.textContent;
                        const newBalance = data.formatted_balance;

                        balanceDisplay.innerHTML = '<i class="fas fa-coins me-1"></i>' + newBalance +
                            '<i class="fas fa-sync-alt ms-1 d-none" id="syncIcon" style="font-size: 0.8em; opacity: 0.7;"></i>';

                        // Show notification if balance changed and notification is requested
                        if (showNotification && oldBalance !== newBalance) {
                            showBalanceUpdateNotification(newBalance);
                        }

                        // Add visual effect when balance updates
                        if (oldBalance !== newBalance) {
                            balanceDisplay.style.transform = 'scale(1.1)';
                            balanceDisplay.style.transition = 'transform 0.3s ease';
                            setTimeout(() => {
                                balanceDisplay.style.transform = 'scale(1)';
                            }, 300);
                        }
                    }
                }
            } catch (error) {
                console.error('Erro ao sincronizar saldo:', error);
            } finally {
                // Hide sync icon
                setTimeout(() => {
                    const syncIcon = document.getElementById('syncIcon');
                    if (syncIcon) {
                        syncIcon.style.animation = '';
                        syncIcon.classList.add('d-none');
                    }
                }, 1000);
            }
        }

        // Add CSS for spin animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // Show balance update notification
        function showBalanceUpdateNotification(newBalance) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-sync-alt me-2"></i>
                <strong>Saldo Atualizado!</strong><br>
                Novo saldo: ${newBalance}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    const bsAlert = new bootstrap.Alert(notification);
                    bsAlert.close();
                }
            }, 5000);
        }

        // Auto-sync balance every 30 seconds for logged in users
        <?php if ($this->session->userdata('user_logged_in')): ?>
        setInterval(() => syncBalance(true), 30000); // Show notification for auto-sync

        // Sync balance when page becomes visible again
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                syncBalance(true); // Show notification when page becomes visible
            }
        });

        // Sync balance when window gains focus
        window.addEventListener('focus', () => syncBalance(true));

        // Initial balance sync after page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => syncBalance(false), 1000); // No notification on initial load
        });

        // Sync balance after game window closes (detect focus return)
        let gameWindowFocused = false;
        window.addEventListener('blur', function() {
            gameWindowFocused = true;
        });

        window.addEventListener('focus', function() {
            if (gameWindowFocused) {
                setTimeout(() => syncBalance(true), 2000); // Sync with notification after game
                gameWindowFocused = false;
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
