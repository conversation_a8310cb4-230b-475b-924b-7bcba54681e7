<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('User_frontend_model');
        $this->load->library('session');
        $this->load->helper(['url', 'form']);
        $this->load->library('form_validation');
    }

    public function index() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            redirect('login');
        }
        
        $data['title'] = 'Plataforma de Games';
        $data['user'] = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        $this->load->view('home/header', $data);
        $this->load->view('home/dashboard', $data);
        $this->load->view('home/footer');
    }

    public function login() {
        // If already logged in, redirect to dashboard
        if ($this->session->userdata('user_logged_in')) {
            redirect('/');
        }

        if ($this->input->method() == 'post') {
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email');
            $this->form_validation->set_rules('password', 'Senha', 'required');

            if ($this->form_validation->run()) {
                $email = $this->input->post('email');
                $password = $this->input->post('password');

                $user = $this->User_frontend_model->verify_login($email, $password);

                if ($user) {
                    $this->session->set_userdata([
                        'user_logged_in' => TRUE,
                        'user_id' => $user['id'],
                        'user_email' => $user['email'],
                        'user_name' => $user['name'],
                        'user_balance' => $user['balance']
                    ]);

                    $this->User_frontend_model->update_last_login($user['id']);
                    redirect('/');
                } else {
                    $this->session->set_flashdata('error', 'Email ou senha inválidos');
                }
            }
        }

        $data['title'] = 'Login - Plataforma de Games';
        $this->load->view('home/login', $data);
    }

    public function register() {
        // If already logged in, redirect to dashboard
        if ($this->session->userdata('user_logged_in')) {
            redirect('/');
        }

        if ($this->input->method() == 'post') {
            $this->form_validation->set_rules('name', 'Nome', 'required|min_length[3]');
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email|is_unique[frontend_users.email]');
            $this->form_validation->set_rules('password', 'Senha', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Confirmar Senha', 'required|matches[password]');

            if ($this->form_validation->run()) {
                $data = [
                    'name' => $this->input->post('name'),
                    'email' => $this->input->post('email'),
                    'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                    'balance' => 0.00,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $user_id = $this->User_frontend_model->create($data);

                if ($user_id) {
                    $this->session->set_flashdata('success', 'Conta criada com sucesso! Faça login para continuar.');
                    redirect('login');
                } else {
                    $this->session->set_flashdata('error', 'Erro ao criar conta. Tente novamente.');
                }
            }
        }

        $data['title'] = 'Registro - Plataforma de Games';
        $this->load->view('home/register', $data);
    }

    public function logout() {
        $this->session->unset_userdata(['user_logged_in', 'user_id', 'user_email', 'user_name', 'user_balance']);
        $this->session->set_flashdata('success', 'Logout realizado com sucesso');
        redirect('login');
    }

    public function games() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        $data['title'] = 'Games Disponíveis';
        $data['user'] = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        $this->load->view('home/header', $data);
        $this->load->view('home/games', $data);
        $this->load->view('home/footer');
    }

    public function play($game_code = null) {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        if (!$game_code) {
            show_404();
        }

        $user = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));

        // Check if user has balance
        if ($user['balance'] <= 0) {
            $data['title'] = 'Saldo Insuficiente';
            $data['user'] = $user;
            $this->load->view('home/header', $data);
            $this->load->view('home/insufficient_balance', $data);
            $this->load->view('home/footer');
            return;
        }

        // Create or sync API user
        $api_user_code = $this->ensure_api_user($user);

        // Load game
        $data['game'] = $game_code;
        $data['user'] = $api_user_code;
        $data['lang'] = 'pt';
        $data['cur'] = 'BRL';

        $this->load->view('game', $data);
    }

    private function ensure_api_user($frontend_user) {
        $this->load->model('User_model');

        $api_user_code = 'USER_' . $frontend_user['id'];

        // Check if API user already exists
        $this->db->where('userCode', $api_user_code);
        $existing_user = $this->db->get('users');

        if ($existing_user->num_rows() == 0) {
            // Create new API user
            $insertData = array(
                'agentCode' => 'AGENT001', // Default test agent
                'userCode' => $api_user_code,
                'aasUserCode' => 'AGENT001' . md5(rand(0, 20000).date('Ymdhhmmss')),
                'createdAt' => date('Y-m-d H:i:s'),
                'balance' => $frontend_user['balance'],
                'status' => 1,
                'apiType' => 1
            );

            $this->User_model->create($insertData);

            // Log user creation
            if (ENVIRONMENT === 'development') {
                log_message('info', "API user created: {$api_user_code} with balance {$frontend_user['balance']}");
            }
        } else {
            // Sync balance if different
            $api_user = $existing_user->row_array();
            if ($api_user['balance'] != $frontend_user['balance']) {
                $this->db->where('userCode', $api_user_code);
                $this->db->update('users', ['balance' => $frontend_user['balance']]);

                // Log balance sync
                if (ENVIRONMENT === 'development') {
                    log_message('info', "API user balance synced: {$api_user_code} -> {$frontend_user['balance']}");
                }
            }
        }

        return $api_user_code;
    }

    public function api_purchase() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        $data['title'] = 'Comprar Créditos API';
        $data['user'] = $this->User_frontend_model->get_by_id($this->session->userdata('user_id'));
        
        $this->load->view('home/header', $data);
        $this->load->view('home/api_purchase', $data);
        $this->load->view('home/footer');
    }

    public function add_balance() {
        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        if ($this->input->method() == 'post') {
            $amount = floatval($this->input->post('amount'));
            $user_id = $this->session->userdata('user_id');
            
            if ($amount > 0) {
                $this->User_frontend_model->add_balance($user_id, $amount);
                
                // Update session balance
                $user = $this->User_frontend_model->get_by_id($user_id);
                $this->session->set_userdata('user_balance', $user['balance']);
                
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['status' => 1, 'msg' => 'Saldo adicionado com sucesso', 'new_balance' => $user['balance']]));
            } else {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode(['status' => 0, 'msg' => 'Valor inválido']));
            }
        }
    }

    public function get_balance() {
        // Set headers to prevent caching
        $this->output->set_header('Cache-Control: no-cache, must-revalidate');
        $this->output->set_header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        $this->output->set_content_type('application/json');

        // Check if user is logged in
        if (!$this->session->userdata('user_logged_in')) {
            $this->output->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        try {
            $user_id = $this->session->userdata('user_id');

            if (!$user_id) {
                $this->output->set_output(json_encode(['status' => 0, 'msg' => 'ID de usuário não encontrado']));
                return;
            }

            $this->load->model('User_frontend_model');
            $user = $this->User_frontend_model->get_by_id($user_id);

            if ($user) {
                // Sync with API user balance if exists
                $this->sync_api_balance($user);

                // Get updated balance
                $user = $this->User_frontend_model->get_by_id($user_id);

                // Update session balance
                $this->session->set_userdata('user_balance', $user['balance']);

                $this->output->set_output(json_encode([
                    'status' => 1,
                    'balance' => floatval($user['balance']),
                    'formatted_balance' => 'R$ ' . number_format($user['balance'], 2, ',', '.')
                ]));
            } else {
                $this->output->set_output(json_encode(['status' => 0, 'msg' => 'Usuário não encontrado']));
            }
        } catch (Exception $e) {
            $this->output->set_output(json_encode(['status' => 0, 'msg' => 'Erro interno: ' . $e->getMessage()]));
        }
    }

    private function sync_api_balance($frontend_user) {
        // Try to find corresponding API user by email
        $this->load->model('User_model');

        // Look for API user with matching email pattern
        $api_user_code = 'USER_' . $frontend_user['id']; // Generate consistent user code

        $this->db->where('userCode', $api_user_code);
        $api_user_query = $this->db->get('users');

        if ($api_user_query->num_rows() > 0) {
            $api_user = $api_user_query->row_array();

            // If API user balance is different, sync it to frontend
            if ($api_user['balance'] != $frontend_user['balance']) {
                $this->User_frontend_model->update_balance($frontend_user['id'], $api_user['balance']);

                // Log the sync for debugging
                if (ENVIRONMENT === 'development') {
                    log_message('info', "Balance synced from API user {$api_user_code}: {$frontend_user['balance']} -> {$api_user['balance']}");
                }
            }
        }
    }

    // Simple test endpoint
    public function test_endpoint() {
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode(['status' => 1, 'msg' => 'Endpoint funcionando', 'timestamp' => date('Y-m-d H:i:s')]));
    }

    // Debug endpoint
    public function debug_session() {
        $this->output->set_content_type('application/json');

        $debug_info = [
            'session_data' => $this->session->all_userdata(),
            'user_logged_in' => $this->session->userdata('user_logged_in'),
            'user_id' => $this->session->userdata('user_id'),
            'user_balance' => $this->session->userdata('user_balance'),
            'session_id' => session_id(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->output->set_output(json_encode($debug_info, JSON_PRETTY_PRINT));
    }

    // Simple balance endpoint for testing
    public function get_balance_simple() {
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'status' => 1,
                'balance' => 200.00,
                'formatted_balance' => 'R$ 200,00',
                'msg' => 'Teste de saldo'
            ]));
    }

    // Test balance sync system
    public function test_balance_sync() {
        $this->output->set_content_type('text/html');

        echo "<h2>Teste de Sincronização de Saldo</h2>";

        // Test 1: Check hooks
        echo "<h3>1. Configuração de Hooks</h3>";
        if ($this->config->item('enable_hooks')) {
            echo "✅ Hooks habilitados<br>";
        } else {
            echo "❌ Hooks desabilitados<br>";
        }

        // Test 2: Check hook files
        echo "<h3>2. Arquivos de Hook</h3>";
        $hook_files = [
            'application/hooks/hooks.balance_sync.php',
            'application/hooks/hooks.profiler.php'
        ];

        foreach ($hook_files as $file) {
            if (file_exists($file)) {
                echo "✅ $file existe<br>";
            } else {
                echo "❌ $file não encontrado<br>";
            }
        }

        // Test 3: Check models
        echo "<h3>3. Modelos</h3>";
        $this->load->model('User_frontend_model');
        $this->load->model('User_model');
        $this->load->model('Agent_model');
        echo "✅ Modelos carregados com sucesso<br>";

        // Test 4: Check database connection
        echo "<h3>4. Conexão com Banco de Dados</h3>";
        if ($this->db->conn_id) {
            echo "✅ Conectado ao banco de dados<br>";

            // Count users
            $frontend_users = $this->db->count_all('frontend_users');
            $api_users = $this->db->count_all('users');
            $agents = $this->db->count_all('agents');

            echo "📊 Usuários Frontend: $frontend_users<br>";
            echo "📊 Usuários API: $api_users<br>";
            echo "📊 Agentes: $agents<br>";
        } else {
            echo "❌ Erro na conexão com banco de dados<br>";
        }

        // Test 5: Check views
        echo "<h3>5. Views</h3>";
        $views = [
            'application/views/agent/create_user.php',
            'application/views/home/<USER>',
            'application/views/home/<USER>'
        ];

        foreach ($views as $view) {
            if (file_exists($view)) {
                echo "✅ $view existe<br>";
            } else {
                echo "❌ $view não encontrado<br>";
            }
        }

        echo "<h3>6. Sistema de Sincronização</h3>";
        echo "✅ Método get_balance() implementado<br>";
        echo "✅ Hook de sincronização criado<br>";
        echo "✅ JavaScript de sincronização automática adicionado<br>";
        echo "✅ Sincronização bidirecional entre API e Frontend<br>";

        echo "<hr>";
        echo "<p><strong>Status:</strong> Sistema de sincronização de saldo implementado com sucesso!</p>";
        echo "<p><strong>Próximos passos:</strong></p>";
        echo "<ul>";
        echo "<li>Faça login no sistema frontend</li>";
        echo "<li>Adicione saldo usando o botão 'Adicionar Saldo'</li>";
        echo "<li>Jogue um game para testar a sincronização</li>";
        echo "<li>Observe o saldo sendo atualizado automaticamente</li>";
        echo "</ul>";
    }

    // Protected route example - returns JSON error if not logged in
    public function protected_api() {
        if (!$this->session->userdata('user_logged_in')) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => 0, 'msg' => 'Acesso negado']));
            return;
        }

        // Your protected API logic here
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode(['status' => 1, 'msg' => 'Acesso autorizado', 'data' => 'Protected content']));
    }
}
