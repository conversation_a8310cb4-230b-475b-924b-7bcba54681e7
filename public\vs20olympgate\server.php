<?php
// Game Server - Dynamic Balance Handler
// This file handles balance requests from the game

// Include CodeIgniter bootstrap
require_once '../../index.php';

// Get CI instance
$CI =& get_instance();
$CI->load->database();
$CI->load->model('User_model');
$CI->load->model('User_frontend_model');

// Get user from URL parameter
$user_code = isset($_GET['user']) ? $_GET['user'] : '';

if (empty($user_code)) {
    // Return default response if no user
    echo "balance=0.00&balance_cash=0.00&balance_bonus=0.00";
    exit;
}

// Get user balance
$balance = 0.00;

// Check if it's a frontend user (USER_X format)
if (strpos($user_code, 'USER_') === 0) {
    $frontend_user_id = str_replace('USER_', '', $user_code);
    $frontend_user = $CI->User_frontend_model->get_by_id($frontend_user_id);
    
    if ($frontend_user) {
        $balance = floatval($frontend_user['balance']);
    }
} else {
    // Check API users table
    $CI->db->where('userCode', $user_code);
    $api_user = $CI->db->get('users')->row_array();
    
    if ($api_user) {
        $balance = floatval($api_user['balance']);
    }
}

// Format balance to 2 decimal places
$formatted_balance = number_format($balance, 2, '.', '');

// Return game response format
echo "tw=0.00&fsmul=1&tmb=&balance={$formatted_balance}&accm=cp&fsmax=15&acci=0&rmul=12~7~3&index=1&balance_cash={$formatted_balance}&reel_set=4&balance_bonus=0.00&na=s&accv=0&fswin=0.00&rs=mc&tmb_win=0.00&l0=&rs_p=0&puri=0&bl=0&stime=" . (time() * 1000) . "&fs=0&sa=&sb=&rs_c=1&sh=5&rs_m=1&fsres=0.00&c=0.10&sver=5&counter=1&l=20&s=&w=0.00";
?>
