<!-- pagina inicio -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">
                    <i class="fas fa-cogs me-2"></i>
                    Configurações do Sistema
                </h4>
            </div>
            <div class="card-body">
                <?php if ($this->session->flashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?= $this->session->flashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($this->session->flashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?= $this->session->flashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="post" action="<?= base_url('admin/settings') ?>">
                    <div class="row">
                        <!-- Configurações Gerais -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-globe me-2"></i>
                                        Configurações Gerais
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">Nome do Site</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" 
                                               value="<?= isset($settings['site_name']) ? $settings['site_name'] : 'GamesPlatform' ?>" 
                                               placeholder="Nome da plataforma">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="site_description" class="form-label">Descrição</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3" 
                                                  placeholder="Descrição da plataforma"><?= isset($settings['site_description']) ? $settings['site_description'] : 'Plataforma de Jogos Online' ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="default_currency" class="form-label">Moeda Padrão</label>
                                        <select class="form-select" id="default_currency" name="default_currency">
                                            <option value="BRL" <?= (isset($settings['default_currency']) && $settings['default_currency'] == 'BRL') ? 'selected' : '' ?>>Real Brasileiro (BRL)</option>
                                            <option value="USD" <?= (isset($settings['default_currency']) && $settings['default_currency'] == 'USD') ? 'selected' : '' ?>>Dólar Americano (USD)</option>
                                            <option value="EUR" <?= (isset($settings['default_currency']) && $settings['default_currency'] == 'EUR') ? 'selected' : '' ?>>Euro (EUR)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="maintenance_mode" class="form-label">Modo Manutenção</label>
                                        <select class="form-select" id="maintenance_mode" name="maintenance_mode">
                                            <option value="0" <?= (isset($settings['maintenance_mode']) && $settings['maintenance_mode'] == '0') ? 'selected' : '' ?>>Desativado</option>
                                            <option value="1" <?= (isset($settings['maintenance_mode']) && $settings['maintenance_mode'] == '1') ? 'selected' : '' ?>>Ativado</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configurações de Jogos -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-gamepad me-2"></i>
                                        Configurações de Jogos
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="default_rtp" class="form-label">RTP Padrão (%)</label>
                                        <input type="number" class="form-control" id="default_rtp" name="default_rtp" 
                                               value="<?= isset($settings['default_rtp']) ? $settings['default_rtp'] : '96.5' ?>" 
                                               min="80" max="99" step="0.1" placeholder="96.5">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="min_bet" class="form-label">Aposta Mínima</label>
                                        <div class="input-group">
                                            <span class="input-group-text">R$</span>
                                            <input type="number" class="form-control" id="min_bet" name="min_bet" 
                                                   value="<?= isset($settings['min_bet']) ? $settings['min_bet'] : '0.10' ?>" 
                                                   min="0.01" step="0.01" placeholder="0.10">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max_bet" class="form-label">Aposta Máxima</label>
                                        <div class="input-group">
                                            <span class="input-group-text">R$</span>
                                            <input type="number" class="form-control" id="max_bet" name="max_bet" 
                                                   value="<?= isset($settings['max_bet']) ? $settings['max_bet'] : '100.00' ?>" 
                                                   min="1" step="0.01" placeholder="100.00">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="demo_mode" class="form-label">Modo Demo</label>
                                        <select class="form-select" id="demo_mode" name="demo_mode">
                                            <option value="1" <?= (isset($settings['demo_mode']) && $settings['demo_mode'] == '1') ? 'selected' : '' ?>>Habilitado</option>
                                            <option value="0" <?= (isset($settings['demo_mode']) && $settings['demo_mode'] == '0') ? 'selected' : '' ?>>Desabilitado</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Configurações de API -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-code me-2"></i>
                                        Configurações de API
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="api_timeout" class="form-label">Timeout da API (segundos)</label>
                                        <input type="number" class="form-control" id="api_timeout" name="api_timeout" 
                                               value="<?= isset($settings['api_timeout']) ? $settings['api_timeout'] : '30' ?>" 
                                               min="5" max="120" placeholder="30">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="api_rate_limit" class="form-label">Limite de Requisições/min</label>
                                        <input type="number" class="form-control" id="api_rate_limit" name="api_rate_limit" 
                                               value="<?= isset($settings['api_rate_limit']) ? $settings['api_rate_limit'] : '100' ?>" 
                                               min="10" max="1000" placeholder="100">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="webhook_url" class="form-label">URL do Webhook</label>
                                        <input type="url" class="form-control" id="webhook_url" name="webhook_url" 
                                               value="<?= isset($settings['webhook_url']) ? $settings['webhook_url'] : '' ?>" 
                                               placeholder="https://exemplo.com/webhook">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Configurações de Segurança -->
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-shield-alt me-2"></i>
                                        Configurações de Segurança
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="session_timeout" class="form-label">Timeout de Sessão (minutos)</label>
                                        <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                               value="<?= isset($settings['session_timeout']) ? $settings['session_timeout'] : '60' ?>" 
                                               min="5" max="480" placeholder="60">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max_login_attempts" class="form-label">Máx. Tentativas de Login</label>
                                        <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                               value="<?= isset($settings['max_login_attempts']) ? $settings['max_login_attempts'] : '5' ?>" 
                                               min="3" max="10" placeholder="5">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="enable_2fa" class="form-label">Autenticação 2FA</label>
                                        <select class="form-select" id="enable_2fa" name="enable_2fa">
                                            <option value="0" <?= (isset($settings['enable_2fa']) && $settings['enable_2fa'] == '0') ? 'selected' : '' ?>>Desabilitado</option>
                                            <option value="1" <?= (isset($settings['enable_2fa']) && $settings['enable_2fa'] == '1') ? 'selected' : '' ?>>Habilitado</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Voltar ao Dashboard
                                </a>
                                
                                <div>
                                    <button type="reset" class="btn btn-outline-secondary me-2">
                                        <i class="fas fa-undo me-1"></i>
                                        Resetar
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        Salvar Configurações
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const minBet = parseFloat(document.getElementById('min_bet').value);
    const maxBet = parseFloat(document.getElementById('max_bet').value);
    
    if (minBet >= maxBet) {
        e.preventDefault();
        alert('A aposta mínima deve ser menor que a aposta máxima');
        return false;
    }
    
    const rtp = parseFloat(document.getElementById('default_rtp').value);
    if (rtp < 80 || rtp > 99) {
        e.preventDefault();
        alert('O RTP deve estar entre 80% e 99%');
        return false;
    }
});
</script>
<!-- pagina fim -->
